using Vsp.PayrollConfiguration.Infrastructure.Interfaces;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using InheritanceLevel = Vsp.PayrollConfiguration.Repository.Entities.Base.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Infrastructure.Queries;

public abstract class GetInheritanceEntityQuery<TModel, TEntity>(
    IFilteredQueryDependencies<ILoketContext> dependencies)
    : FilteredQuery<TModel, TEntity, InheritanceLevel, ILoketContext>(dependencies),
        IGetInheritanceEntityQuery<TModel, TEntity>
    where TModel : class, new()
    where TEntity : GeneratedIdEntity, IInheritanceEntity, new()
{
    public async Task<IOperationResult<TModel>> ExecuteAsync(Guid id) =>
        await Execute(id);

    public async Task<IListOperationResult<TModel>> ExecuteListAsync(Guid inheritanceLevelId) =>
        await ExecuteList(inheritanceLevelId);

    protected override IQueryable<TEntity> ApplyQueryParameters(IQueryable<TEntity> query, Guid id) =>
        query.Where(GeneratedIdHelper.ConstructWhere<TEntity>(id));

    public override Expression<Func<TEntity, bool>>? FilterCollectionByExpression(Guid id) =>
        x => x.InheritanceLevel.Id == id;
}
