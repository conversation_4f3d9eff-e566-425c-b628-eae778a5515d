namespace Vsp.PayrollConfiguration.Repository.Entities.TODO;

internal class SpaarregelingConfiguration : GeneratedIdEntityTypeConfigurationBase<Spaarregeling>
{
    public override void Configure(EntityTypeBuilder<Spaarregeling> builder)
    {
        base.Configure(builder);

        builder.ToTable("<PERSON><PERSON>regel<PERSON>", "Ulsa");
        builder.<PERSON><PERSON>(x => new { x.WerkgeverID, x.Jaar<PERSON>, x.SpaarregelingID, x.VerloningsPeriodeID });
    }
}
