{"_embedded": [{"collectiveLaborAgreement": {"comment": "<br>", "description": "QA_PayrollConfiguration_4Week_CLA", "id": "1aaecb1e-f8e4-4854-a314-c863b88215ca"}, "comment": "<br>", "description": "QA_PayrollConfiguration_4Week_WM", "id": "6eb0fc41-e82e-4e7d-9736-c87e2c4fe258"}, {"collectiveLaborAgreement": {"comment": "<br>", "description": "QA_PayrollConfiguration_Week_CLA", "id": "c0a9de91-390a-4f12-967a-a17dfbc94cc7"}, "comment": "<br>", "description": "QA_PayrollConfiguration_Week_WM", "id": "bf093626-7201-4e56-89d4-2d8760728771"}, {"collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA", "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342"}, "comment": "Empty WM for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_WM_ForLevel_PA", "id": "5e010bed-611d-469b-a220-5253e3896f47"}, {"collectiveLaborAgreement": {"comment": "Empty CLA for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_CLA_ForLevel_WM", "id": "8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf"}, "comment": "Empty WM for unit and integration tests. DO NOT EDIT!", "description": "QA_PayrollConfiguration1_WM_ForLevel_WM", "id": "a77d7e45-2612-450a-86d0-485baecee6ad"}], "currentPage": 1, "messages": [], "pageSize": 250, "totalPages": 1, "totalSize": 4, "version": {"obsoleteDate": null, "resourceVersion": "2018-01-01"}}