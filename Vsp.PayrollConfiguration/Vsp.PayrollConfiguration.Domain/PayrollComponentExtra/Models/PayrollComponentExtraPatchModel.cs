using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Converters;

namespace Vsp.PayrollConfiguration.Domain.PayrollComponentExtra.Models;

public class PayrollComponentExtraPatchModel
{
    [JsonIgnore]
    public Guid PayrollComponentId { get; set; }

    [JsonConverter(typeof(TrimStringConverter))]
    public string? DeviatingDescription { get; set; }

    public KeyModel? RouteType { get; set; }
}