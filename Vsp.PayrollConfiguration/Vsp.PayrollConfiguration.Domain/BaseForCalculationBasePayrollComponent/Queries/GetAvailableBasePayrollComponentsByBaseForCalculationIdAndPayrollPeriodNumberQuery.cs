using AutoMapper.QueryableExtensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Extensions;
using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;
using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Repository.Interfaces;
using AutoMapper;
using Microsoft.EntityFrameworkCore;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Queries;

internal class GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery(
    ILoketContext context,
    IMapper mapper) : IGetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberQuery
{
    public async Task<ListOperationResult<PayrollComponentMinimizedModel>> Execute(Guid baseForCalculationId, int payrollPeriodNumber)
    {
        var query = context
            .AvailableBasePayrollComponents(baseForCalculationId, payrollPeriodNumber)
            .ProjectTo<PayrollComponentMinimizedModel>(mapper.ConfigurationProvider);

        var result = await query.ToArrayAsync();
        return new ListOperationResult<PayrollComponentMinimizedModel>(result);
    }
}