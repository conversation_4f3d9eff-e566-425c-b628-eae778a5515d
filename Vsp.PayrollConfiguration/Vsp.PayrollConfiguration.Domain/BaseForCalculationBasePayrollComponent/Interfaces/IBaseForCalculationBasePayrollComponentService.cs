using Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Models;
using Vsp.PayrollConfiguration.Domain.PayrollComponent.Models;

namespace Vsp.PayrollConfiguration.Domain.BaseForCalculationBasePayrollComponent.Interfaces;

public interface IBaseForCalculationBasePayrollComponentService
{
    Task<IListOperationResult<BaseForCalculationBasePayrollComponentModel>> GetBaseForCalculationBasePayrollComponentsByBaseForCalculationIdAsync(Guid baseForCalculationId);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>> PostBaseForCalculationBasePayrollComponentByBaseForCalculationIdAsync(Guid baseForCalculationId, BaseForCalculationBasePayrollComponentPostModel postModel);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentModel>> PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(Guid baseForCalculationId, Guid baseForCalculationBasePayrollComponentId, BaseForCalculationBasePayrollComponentPatchModel patchModel);
    Task<IOperationResult<NoResult>> DeleteBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentIdAsync(Guid baseForCalculationId, Guid baseForCalculationBasePayrollComponentId);
    Task<IOperationResult<BaseForCalculationBasePayrollComponentMetadataModel>> GetBaseForCalculationBasePayrollComponentMetadataByProviderIdAsync(Guid providerId);
    Task<IListOperationResult<PayrollComponentModel>> GetAvailableBasePayrollComponentsByBaseForCalculationIdAndPayrollPeriodNumberAsync(Guid baseForCalculationId, int payrollPeriodNumber);
} 