using Vsp.PayrollConfiguration.Domain.Shared.Models;
using Vsp.PayrollConfiguration.Infrastructure.Models;
using Vsp.PayrollConfiguration.Repository.Entities.CodeTable;
using Vsp.PayrollConfiguration.Repository.Enums;
using InheritanceLevel = Vsp.AuthorizationService.Internal.ApiProtocol.Authorization.InheritanceLevel;

namespace Vsp.PayrollConfiguration.Domain.Shared.Mappers;

internal class SharedProfile : Profile
{
    public SharedProfile()
    {
        CreateMap<CodeTable, KeyValueModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.Code))
            .ForMember(dst => dst.Value, opt => opt.MapFrom(src => src.Omschrijving));

        CreateMap<CodeTableNullable, KeyValueModel?>()
            .ConvertUsing(src => src == null || src.Code == 0 ? null : new KeyValueModel { Key = src.Code, Value = src.Omschrijving });

        CreateMap<int, KeyModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src));

        CreateMap<KeyModel?, int?>()
            .ConvertUsing(src => src == null ? 0 : src.Key);

        CreateMap<KeyModel?, int>()
            .ConvertUsing(src => src == null || src.Key == null ? 0 : src.Key.Value);

        CreateMap<Repository.Entities.Base.InheritanceLevel, InheritanceLevelModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.Type, opt => opt.MapFrom(src => src));

        CreateMap<Repository.Entities.Base.InheritanceLevel, InheritanceLevelTypeModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.InheritanceLevelInfo.Type))
            .ForMember(dst => dst.Value, opt => opt.MapFrom(src => ((InheritanceLevel)src.InheritanceLevelInfo.Type).ToString()));

        CreateMap<int, InheritanceLevelTypeModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src))
            .ForMember(dst => dst.Value, opt => opt.MapFrom(src => ((InheritanceLevel)src).ToString()));

        CreateMap<int, bool>().ConvertUsing(src => src == (int)YesNo.Yes);
        CreateMap<int, bool?>().ConvertUsing(src => src == (int)YesNo.Yes);
        CreateMap<bool, int>().ConvertUsing(src => src ? (int)YesNo.Yes : (int)YesNo.No);
        CreateMap<bool, int?>().ConvertUsing(src => src ? (int)YesNo.Yes : (int)YesNo.No);

        CreateMap<Repository.Entities.Year, YearMinimizedModel>()
            .ForMember(dst => dst.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dst => dst.InheritanceLevel, opt => opt.MapFrom(src => src.InheritanceLevel))
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.PayrollPeriodType, opt => opt.MapFrom(src => src.CtPayrollPeriodType));

        CreateMap<Component, PayrollComponentMinimizedModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.ComponentId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.Category, opt => opt.MapFrom(src => src.CtCategory));

        CreateMap<ComponentGeneral, PayrollComponentMinimizedModel>()
            .ForMember(dst => dst.Key, opt => opt.MapFrom(src => src.ComponentId))
            .ForMember(dst => dst.Description, opt => opt.MapFrom(src => src.Description))
            .ForMember(dst => dst.Category, opt => opt.MapFrom(src => src.CtCategory));

        CreateMap<PayrollPeriodNumberModel?, int?>()
            .ConvertUsing(src => src == null ? 0 : src.PeriodNumber);

        CreateMap<PayrollPeriodNumberModel?, int>()
            .ConvertUsing(src => src == null || src.PeriodNumber == null ? 0 : src.PeriodNumber.Value);

        CreateMap<PayrollPeriod, PayrollPeriodPostModel>()
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.PeriodNumber, opt => opt.MapFrom(src => src.PayrollPeriodId));

        CreateMap<PayrollPeriod, PayrollPeriodModel>()
            .IncludeBase<PayrollPeriod, PayrollPeriodPostModel>()
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.YearPeriod))
            .ForMember(dst => dst.PeriodStartDate, opt => opt.MapFrom(src => src.StartDate))
            .ForMember(dst => dst.PeriodEndDate, opt => opt.MapFrom(src => src.EndDate));

        CreateMap<PayrollPeriodPayrollTaxReturn, PayrollPeriodModel>()
            .ForMember(dst => dst.Year, opt => opt.MapFrom(src => src.YearId))
            .ForMember(dst => dst.PeriodNumber, opt => opt.MapFrom(src => src.PayrollPeriodId))
            .ForMember(dst => dst.PayrollPeriodId, opt => opt.MapFrom(src => src.YearId * 100 + src.PayrollPeriodId))
            .ForMember(dst => dst.PeriodStartDate, opt => opt.MapFrom(src => src.StartDate))
            .ForMember(dst => dst.PeriodEndDate, opt => opt.MapFrom(src => src.EndDate));

        // For cloning objects with AutoMapper
        CreateMap<PayrollPeriodNumberModel, PayrollPeriodNumberModel>();
    }
}